/// ✅ API重定向文件
/// 
/// 此文件已被重构并合并到 performance_manager.dart
/// 为保持向后兼容性，提供重定向导出

// 重定向到新的统一性能管理器
export 'package:smartguard/services/performance_manager.dart';

import 'package:smartguard/services/performance_manager.dart';
import 'package:smartguard/models/recognition_algorithm.dart';
import 'package:smartguard/utils/app_logger.dart';

/// ⚠️ 已废弃：请使用 PerformanceManager 替代
/// 
/// 此类已被合并到 PerformanceManager 中，提供更统一的性能监控功能
@Deprecated('使用 PerformanceManager.recordMLKitPerformance() 替代')
class MLKitPerformanceMonitor {
  static MLKitPerformanceMonitor? _instance;
  static MLKitPerformanceMonitor get instance => _instance ??= MLKitPerformanceMonitor._();
  
  MLKitPerformanceMonitor._();
  
  /// 重定向到新的性能管理器
  final PerformanceManager _performanceManager = PerformanceManager.instance;
  
  /// ⚠️ 已废弃：请使用 PerformanceManager.initialize() 替代
  @Deprecated('使用 PerformanceManager.initialize() 替代')
  void initialize() {
    AppLogger.warning('⚠️ MLKitPerformanceMonitor已废弃，请使用PerformanceManager');
    _performanceManager.initialize();
  }
  
  /// ⚠️ 已废弃：请使用 PerformanceManager.recordMLKitPerformance() 替代
  @Deprecated('使用 PerformanceManager.recordMLKitPerformance() 替代')
  void recordRecognitionPerformance({
    required RecognitionAlgorithm algorithm,
    required Duration processingTime,
    required bool successful,
    required double confidence,
    Map<String, dynamic>? additionalData,
  }) {
    AppLogger.warning('⚠️ MLKitPerformanceMonitor.recordRecognitionPerformance已废弃');
    _performanceManager.recordMLKitPerformance(
      algorithm: algorithm,
      processingTime: processingTime,
      successful: successful,
      confidence: confidence,
      additionalData: additionalData,
    );
  }
  
  /// ⚠️ 已废弃：请使用 PerformanceManager.getMLKitPerformanceStats() 替代
  @Deprecated('使用 PerformanceManager.getMLKitPerformanceStats() 替代')
  Map<String, dynamic> getPerformanceStats() {
    AppLogger.warning('⚠️ MLKitPerformanceMonitor.getPerformanceStats已废弃');
    return _performanceManager.getMLKitPerformanceStats();
  }
  
  /// ⚠️ 已废弃：请使用 PerformanceManager.dispose() 替代
  @Deprecated('使用 PerformanceManager.dispose() 替代')
  void dispose() {
    AppLogger.warning('⚠️ MLKitPerformanceMonitor.dispose已废弃');
    // 不调用_performanceManager.dispose()，因为它可能被其他地方使用
  }
}

/// ⚠️ 已废弃：兼容性类型定义
@Deprecated('使用 PerformanceManager 中的相关类型替代')
class PerformanceMetric {
  final DateTime timestamp;
  final RecognitionAlgorithm algorithm;
  final Duration processingTime;
  final bool successful;
  final double confidence;
  final Map<String, dynamic>? additionalData;

  PerformanceMetric({
    required this.timestamp,
    required this.algorithm,
    required this.processingTime,
    required this.successful,
    required this.confidence,
    this.additionalData,
  });
}

/// ⚠️ 已废弃：兼容性类型定义
@Deprecated('使用 PerformanceManager 中的相关类型替代')
class AlgorithmStats {
  final int totalRecognitions;
  final int successfulRecognitions;
  final Duration totalProcessingTime;
  final double averageConfidence;

  AlgorithmStats({
    required this.totalRecognitions,
    required this.successfulRecognitions,
    required this.totalProcessingTime,
    required this.averageConfidence,
  });
  
  double get successRate => totalRecognitions > 0 ? successfulRecognitions / totalRecognitions : 0.0;
  Duration get averageProcessingTime => totalRecognitions > 0 
      ? Duration(milliseconds: totalProcessingTime.inMilliseconds ~/ totalRecognitions)
      : Duration.zero;
}

/// ⚠️ 已废弃：兼容性类型定义
@Deprecated('使用 PerformanceManager 中的相关类型替代')
class PerformanceUpdate {
  final String type;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  PerformanceUpdate({
    required this.type,
    required this.data,
    required this.timestamp,
  });
}
