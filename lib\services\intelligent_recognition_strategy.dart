import 'dart:async';
import 'dart:math';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/services/ml_kit_algorithm_optimizer.dart';
import 'package:loadguard/services/mlkit_text_recognition_service.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 智能识别策略管理器
/// 根据历史数据和实时分析选择最优的识别策略
class IntelligentRecognitionStrategy {
  final Map<String, AlgorithmPerformanceData> _performanceHistory = {};
  final Map<String, int> _algorithmUsageCount = {};
  final List<RecognitionAnalytics> _recentAnalytics = [];
  
  static const int _maxAnalyticsHistory = 100;
  static const double _performanceDecayFactor = 0.95;
  
  /// 获取智能推荐的识别策略
  Future<IntelligentStrategy> getRecommendedStrategy({
    required String imagePath,
    String? imageCategory,
    Map<String, dynamic>? context,
  }) async {
    try {
      AppLogger.info('🧠 开始智能策略分析: $imagePath');
      
      // 1. 基础算法选择
      final algorithmSelection = await MLKitAlgorithmOptimizer.selectOptimalAlgorithm(imagePath);
      
      // 2. 历史性能分析
      final performanceInsights = _analyzeHistoricalPerformance(algorithmSelection.qualityMetrics);
      
      // 3. 上下文适配
      final contextualAdjustments = _applyContextualAdjustments(
        algorithmSelection,
        imageCategory,
        context,
      );
      
      // 4. 生成智能策略
      final strategy = IntelligentStrategy(
        primaryAlgorithm: contextualAdjustments.primary,
        fallbackAlgorithms: contextualAdjustments.fallback,
        qualityMetrics: algorithmSelection.qualityMetrics,
        performanceInsights: performanceInsights,
        optimizationSuggestions: algorithmSelection.optimizationSuggestions,
        confidence: _calculateStrategyConfidence(algorithmSelection, performanceInsights),
        estimatedProcessingTime: _estimateProcessingTime(contextualAdjustments.primary),
        recommendedRetries: _calculateRecommendedRetries(algorithmSelection.qualityMetrics),
      );
      
      AppLogger.info('✅ 智能策略生成完成: ${strategy.primaryAlgorithm.displayName}');
      return strategy;
      
    } catch (e) {
      AppLogger.error('❌ 智能策略分析失败', error: e);
      return _getDefaultStrategy();
    }
  }
  
  /// 执行智能识别
  Future<IntelligentRecognitionResult> executeIntelligentRecognition({
    required String imagePath,
    IntelligentStrategy? strategy,
    String? presetProductCode,
    String? presetBatchNumber,
    Function(double progress, String status)? onProgress,
  }) async {
    final startTime = DateTime.now();
    
    try {
      onProgress?.call(0.1, '分析智能策略...');
      
      // 1. 获取或使用提供的策略
      strategy ??= await getRecommendedStrategy(imagePath: imagePath);
      
      onProgress?.call(0.2, '准备智能识别...');
      
      // 2. 执行主要策略
      var result = await _executeStrategyWithRetries(
        strategy,
        imagePath,
        presetProductCode: presetProductCode,
        presetBatchNumber: presetBatchNumber,
        onProgress: (progress, status) {
          onProgress?.call(0.2 + progress * 0.6, status);
        },
      );
      
      onProgress?.call(0.8, '分析识别结果...');
      
      // 3. 结果质量评估
      final qualityAssessment = _assessResultQuality(result, strategy);
      
      // 4. 自适应学习
      await _updatePerformanceData(strategy, result, qualityAssessment);
      
      onProgress?.call(1.0, '智能识别完成');
      
      final processingTime = DateTime.now().difference(startTime);
      
      return IntelligentRecognitionResult(
        optimizedResult: result,
        strategy: strategy,
        qualityAssessment: qualityAssessment,
        processingTime: processingTime,
        adaptiveLearningApplied: true,
      );
      
    } catch (e) {
      AppLogger.error('❌ 智能识别执行失败', error: e);
      rethrow;
    }
  }
  
  /// 分析历史性能
  PerformanceInsights _analyzeHistoricalPerformance(ImageQualityMetrics metrics) {
    final relevantHistory = _findRelevantHistory(metrics);
    
    if (relevantHistory.isEmpty) {
      return PerformanceInsights.defaultInsights();
    }
    
    // 计算各算法的平均性能
    final algorithmPerformance = <RecognitionAlgorithm, double>{};
    for (final data in relevantHistory) {
      algorithmPerformance[data.algorithm] = 
          (algorithmPerformance[data.algorithm] ?? 0.0) + data.successRate;
    }
    
    // 找出最佳算法
    final bestAlgorithm = algorithmPerformance.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
    
    // 计算置信度
    final confidence = algorithmPerformance[bestAlgorithm]! / relevantHistory.length;
    
    return PerformanceInsights(
      bestPerformingAlgorithm: bestAlgorithm,
      averageSuccessRate: confidence,
      totalSamples: relevantHistory.length,
      recommendationStrength: confidence > 0.8 ? 'strong' : confidence > 0.6 ? 'moderate' : 'weak',
    );
  }
  
  /// 应用上下文调整
  AlgorithmSelectionResult _applyContextualAdjustments(
    AlgorithmSelection baseSelection,
    String? imageCategory,
    Map<String, dynamic>? context,
  ) {
    var primary = baseSelection.primary;
    var fallback = List<RecognitionAlgorithm>.from(baseSelection.fallback);
    
    // 根据图像类别调整
    if (imageCategory != null) {
      switch (imageCategory.toLowerCase()) {
        case 'product_label':
          if (primary == RecognitionAlgorithm.standardLatin) {
            primary = RecognitionAlgorithm.enhancedLatin;
          }
          break;
        case 'batch_number':
          if (!fallback.contains(RecognitionAlgorithm.smallText)) {
            fallback.insert(0, RecognitionAlgorithm.smallText);
          }
          break;
        case 'mixed_content':
          primary = RecognitionAlgorithm.mixedLanguage;
          break;
      }
    }
    
    // 根据上下文调整
    if (context != null) {
      final lightingCondition = context['lighting'] as String?;
      if (lightingCondition == 'poor') {
        primary = RecognitionAlgorithm.lowLight;
        fallback.insert(0, RecognitionAlgorithm.highContrast);
      }
      
      final urgency = context['urgency'] as String?;
      if (urgency == 'high') {
        // 优先选择快速算法
        fallback = fallback.where((alg) => 
          alg != RecognitionAlgorithm.perspectiveCorrected &&
          alg != RecognitionAlgorithm.noiseReduction
        ).toList();
      }
    }
    
    return AlgorithmSelectionResult(
      primary: primary,
      fallback: fallback,
      confidence: baseSelection.confidence,
    );
  }
  
  /// 计算策略置信度
  double _calculateStrategyConfidence(
    AlgorithmSelection selection,
    PerformanceInsights insights,
  ) {
    final baseConfidence = selection.confidence;
    final historyBonus = insights.averageSuccessRate * 0.3;
    final qualityFactor = selection.qualityMetrics.overallQuality * 0.2;
    
    return (baseConfidence + historyBonus + qualityFactor).clamp(0.0, 1.0);
  }
  
  /// 估算处理时间
  Duration _estimateProcessingTime(RecognitionAlgorithm algorithm) {
    // 基于算法复杂度估算处理时间
    const baseTime = Duration(milliseconds: 1500);
    
    switch (algorithm) {
      case RecognitionAlgorithm.standardLatin:
        return baseTime;
      case RecognitionAlgorithm.enhancedLatin:
        return Duration(milliseconds: (baseTime.inMilliseconds * 1.2).round());
      case RecognitionAlgorithm.chineseOptimized:
      case RecognitionAlgorithm.mixedLanguage:
        return Duration(milliseconds: (baseTime.inMilliseconds * 1.5).round());
      case RecognitionAlgorithm.perspectiveCorrected:
      case RecognitionAlgorithm.noiseReduction:
        return Duration(milliseconds: (baseTime.inMilliseconds * 2.0).round());
      default:
        return Duration(milliseconds: (baseTime.inMilliseconds * 1.3).round());
    }
  }
  
  /// 计算推荐重试次数
  int _calculateRecommendedRetries(ImageQualityMetrics metrics) {
    if (metrics.overallQuality >= 0.8) return 1;
    if (metrics.overallQuality >= 0.6) return 2;
    if (metrics.overallQuality >= 0.4) return 3;
    return 4;
  }
  
  /// 执行策略并重试
  Future<OptimizedRecognitionResult> _executeStrategyWithRetries(
    IntelligentStrategy strategy,
    String imagePath, {
    String? presetProductCode,
    String? presetBatchNumber,
    Function(double progress, String status)? onProgress,
  }) async {
    var lastResult = await MLKitAlgorithmOptimizer.processWithOptimization(
      imagePath: imagePath,
      algorithmSelection: AlgorithmSelection(
        primary: strategy.primaryAlgorithm,
        fallback: strategy.fallbackAlgorithms,
        qualityMetrics: strategy.qualityMetrics,
        optimizationSuggestions: strategy.optimizationSuggestions,
        confidence: strategy.confidence,
      ),
      presetProductCode: presetProductCode,
      presetBatchNumber: presetBatchNumber,
      onProgress: onProgress,
    );
    
    // 如果成功或已用完重试次数，返回结果
    if (lastResult.isSuccessful || strategy.recommendedRetries <= 1) {
      return lastResult;
    }
    
    // 尝试重试
    for (int retry = 1; retry < strategy.recommendedRetries; retry++) {
      try {
        onProgress?.call(0.0, '重试识别 ($retry/${strategy.recommendedRetries - 1})...');
        
        lastResult = await MLKitAlgorithmOptimizer.processWithOptimization(
          imagePath: imagePath,
          algorithmSelection: AlgorithmSelection(
            primary: strategy.fallbackAlgorithms.isNotEmpty 
                ? strategy.fallbackAlgorithms[min(retry - 1, strategy.fallbackAlgorithms.length - 1)]
                : strategy.primaryAlgorithm,
            fallback: strategy.fallbackAlgorithms,
            qualityMetrics: strategy.qualityMetrics,
            optimizationSuggestions: strategy.optimizationSuggestions,
            confidence: strategy.confidence * 0.9, // 降低置信度
          ),
          presetProductCode: presetProductCode,
          presetBatchNumber: presetBatchNumber,
          onProgress: onProgress,
        );
        
        if (lastResult.isSuccessful) {
          AppLogger.info('✅ 重试成功: 第${retry}次重试');
          break;
        }
      } catch (e) {
        AppLogger.warning('⚠️ 重试失败: 第${retry}次重试 - $e');
        continue;
      }
    }
    
    return lastResult;
  }
  
  /// 评估结果质量
  ResultQualityAssessment _assessResultQuality(
    OptimizedRecognitionResult result,
    IntelligentStrategy strategy,
  ) {
    double qualityScore = 0.0;
    final factors = <String>[];
    
    // 基础成功性评估
    if (result.isSuccessful) {
      qualityScore += 0.4;
      factors.add('识别成功');
    }
    
    // 处理时间评估
    final expectedTime = strategy.estimatedProcessingTime;
    final actualTime = result.processingTime;
    if (actualTime <= expectedTime) {
      qualityScore += 0.2;
      factors.add('处理时间符合预期');
    } else if (actualTime <= expectedTime * 1.5) {
      qualityScore += 0.1;
      factors.add('处理时间略超预期');
    }
    
    // 算法匹配度评估
    if (result.algorithmUsed == strategy.primaryAlgorithm) {
      qualityScore += 0.2;
      factors.add('使用了推荐算法');
    } else if (strategy.fallbackAlgorithms.contains(result.algorithmUsed)) {
      qualityScore += 0.1;
      factors.add('使用了备用算法');
    }
    
    // 优化应用评估
    if (result.optimizationApplied && strategy.qualityMetrics.needsPreprocessing) {
      qualityScore += 0.1;
      factors.add('应用了必要的优化');
    }
    
    // 置信度评估
    if (strategy.confidence >= 0.8) {
      qualityScore += 0.1;
      factors.add('高置信度策略');
    }
    
    return ResultQualityAssessment(
      overallScore: qualityScore.clamp(0.0, 1.0),
      qualityLevel: qualityScore >= 0.8 ? 'excellent' : 
                   qualityScore >= 0.6 ? 'good' : 
                   qualityScore >= 0.4 ? 'fair' : 'poor',
      contributingFactors: factors,
    );
  }
  
  /// 更新性能数据
  Future<void> _updatePerformanceData(
    IntelligentStrategy strategy,
    OptimizedRecognitionResult result,
    ResultQualityAssessment assessment,
  ) async {
    try {
      // 更新算法性能数据
      final key = _generatePerformanceKey(strategy.qualityMetrics);
      final existing = _performanceHistory[key] ?? AlgorithmPerformanceData(
        algorithm: result.algorithmUsed,
        successRate: 0.0,
        averageProcessingTime: Duration.zero,
        sampleCount: 0,
      );
      
      // 应用衰减因子更新性能数据
      final newSuccessRate = (existing.successRate * _performanceDecayFactor + 
                             (result.isSuccessful ? 1.0 : 0.0)) / 
                            (existing.sampleCount * _performanceDecayFactor + 1);
      
      _performanceHistory[key] = AlgorithmPerformanceData(
        algorithm: result.algorithmUsed,
        successRate: newSuccessRate,
        averageProcessingTime: Duration(
          milliseconds: ((existing.averageProcessingTime.inMilliseconds * _performanceDecayFactor + 
                         result.processingTime.inMilliseconds) / 
                        (existing.sampleCount * _performanceDecayFactor + 1)).round(),
        ),
        sampleCount: existing.sampleCount + 1,
      );
      
      // 更新使用计数
      _algorithmUsageCount[result.algorithmUsed.name] = 
          (_algorithmUsageCount[result.algorithmUsed.name] ?? 0) + 1;
      
      // 添加到分析历史
      _recentAnalytics.add(RecognitionAnalytics(
        timestamp: DateTime.now(),
        algorithm: result.algorithmUsed,
        qualityMetrics: strategy.qualityMetrics,
        processingTime: result.processingTime,
        successful: result.isSuccessful,
        qualityScore: assessment.overallScore,
      ));
      
      // 保持历史记录大小
      if (_recentAnalytics.length > _maxAnalyticsHistory) {
        _recentAnalytics.removeAt(0);
      }
      
      AppLogger.debug('📊 性能数据已更新: ${result.algorithmUsed.name}');
    } catch (e) {
      AppLogger.error('❌ 更新性能数据失败', error: e);
    }
  }
  
  /// 查找相关历史数据
  List<AlgorithmPerformanceData> _findRelevantHistory(ImageQualityMetrics metrics) {
    // 简化实现：返回所有历史数据
    return _performanceHistory.values.toList();
  }
  
  /// 生成性能键
  String _generatePerformanceKey(ImageQualityMetrics metrics) {
    return '${metrics.qualityLevel}_${metrics.hasBlur}_${metrics.hasLowContrast}';
  }
  
  /// 获取默认策略
  IntelligentStrategy _getDefaultStrategy() {
    return IntelligentStrategy(
      primaryAlgorithm: RecognitionAlgorithm.standardLatin,
      fallbackAlgorithms: [RecognitionAlgorithm.enhancedLatin],
      qualityMetrics: ImageQualityMetrics.defaultMetrics(),
      performanceInsights: PerformanceInsights.defaultInsights(),
      optimizationSuggestions: ['使用默认识别策略'],
      confidence: 0.5,
      estimatedProcessingTime: const Duration(milliseconds: 1500),
      recommendedRetries: 2,
    );
  }
  
  /// 获取性能统计
  Map<String, dynamic> getPerformanceStatistics() {
    return {
      'totalRecognitions': _recentAnalytics.length,
      'averageSuccessRate': _recentAnalytics.isEmpty ? 0.0 :
          _recentAnalytics.where((a) => a.successful).length / _recentAnalytics.length,
      'averageProcessingTime': _recentAnalytics.isEmpty ? 0 :
          _recentAnalytics.map((a) => a.processingTime.inMilliseconds).reduce((a, b) => a + b) / _recentAnalytics.length,
      'algorithmUsage': Map.from(_algorithmUsageCount),
      'performanceHistory': _performanceHistory.length,
    };
  }
}

/// 智能策略
class IntelligentStrategy {
  final RecognitionAlgorithm primaryAlgorithm;
  final List<RecognitionAlgorithm> fallbackAlgorithms;
  final ImageQualityMetrics qualityMetrics;
  final PerformanceInsights performanceInsights;
  final List<String> optimizationSuggestions;
  final double confidence;
  final Duration estimatedProcessingTime;
  final int recommendedRetries;

  const IntelligentStrategy({
    required this.primaryAlgorithm,
    required this.fallbackAlgorithms,
    required this.qualityMetrics,
    required this.performanceInsights,
    required this.optimizationSuggestions,
    required this.confidence,
    required this.estimatedProcessingTime,
    required this.recommendedRetries,
  });
}

/// 性能洞察
class PerformanceInsights {
  final RecognitionAlgorithm bestPerformingAlgorithm;
  final double averageSuccessRate;
  final int totalSamples;
  final String recommendationStrength;

  const PerformanceInsights({
    required this.bestPerformingAlgorithm,
    required this.averageSuccessRate,
    required this.totalSamples,
    required this.recommendationStrength,
  });

  factory PerformanceInsights.defaultInsights() {
    return const PerformanceInsights(
      bestPerformingAlgorithm: RecognitionAlgorithm.standardLatin,
      averageSuccessRate: 0.7,
      totalSamples: 0,
      recommendationStrength: 'weak',
    );
  }
}

/// 算法性能数据
class AlgorithmPerformanceData {
  final RecognitionAlgorithm algorithm;
  final double successRate;
  final Duration averageProcessingTime;
  final int sampleCount;

  const AlgorithmPerformanceData({
    required this.algorithm,
    required this.successRate,
    required this.averageProcessingTime,
    required this.sampleCount,
  });
}

/// 识别分析数据
class RecognitionAnalytics {
  final DateTime timestamp;
  final RecognitionAlgorithm algorithm;
  final ImageQualityMetrics qualityMetrics;
  final Duration processingTime;
  final bool successful;
  final double qualityScore;

  const RecognitionAnalytics({
    required this.timestamp,
    required this.algorithm,
    required this.qualityMetrics,
    required this.processingTime,
    required this.successful,
    required this.qualityScore,
  });
}

/// 智能识别结果
class IntelligentRecognitionResult {
  final OptimizedRecognitionResult optimizedResult;
  final IntelligentStrategy strategy;
  final ResultQualityAssessment qualityAssessment;
  final Duration processingTime;
  final bool adaptiveLearningApplied;

  const IntelligentRecognitionResult({
    required this.optimizedResult,
    required this.strategy,
    required this.qualityAssessment,
    required this.processingTime,
    required this.adaptiveLearningApplied,
  });

  bool get isSuccessful => optimizedResult.isSuccessful;

  Map<String, dynamic> toDetailedAnalytics() {
    return {
      ...optimizedResult.toAnalyticsData(),
      'strategyConfidence': strategy.confidence,
      'qualityAssessment': qualityAssessment.overallScore,
      'adaptiveLearning': adaptiveLearningApplied,
      'totalProcessingTime': processingTime.inMilliseconds,
      'recommendedRetries': strategy.recommendedRetries,
    };
  }
}

/// 结果质量评估
class ResultQualityAssessment {
  final double overallScore;
  final String qualityLevel;
  final List<String> contributingFactors;

  const ResultQualityAssessment({
    required this.overallScore,
    required this.qualityLevel,
    required this.contributingFactors,
  });
}

/// 智能识别策略Provider
final intelligentRecognitionStrategyProvider = Provider<IntelligentRecognitionStrategy>((ref) {
  return IntelligentRecognitionStrategy();
});
