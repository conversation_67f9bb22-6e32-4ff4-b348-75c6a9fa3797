/// 🔍 识别管理服务
///
/// 专门负责照片识别相关的管理，包括：
/// - 识别流程控制
/// - 识别状态管理
/// - 识别结果处理
/// - 识别统计更新
/// 从TaskService中提取，减少其复杂度
library recognition_management_service;

import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 识别状态
enum RecognitionState {
  idle,        // 空闲
  starting,    // 启动中
  processing,  // 处理中
  completed,   // 已完成
  failed,      // 失败
  cancelled,   // 已取消
}

/// 识别管理服务
class RecognitionManagementService {
  
  /// 开始照片识别
  Future<void> startRecognition(TaskModel task, String photoId) async {
    try {
      AppLogger.info('🔍 开始照片识别: $photoId', tag: 'RecognitionMgmt');
      
      final photoIndex = task.photos.indexWhere((p) => p.id == photoId);
      if (photoIndex == -1) {
        throw Exception('照片不存在: $photoId');
      }

      final photo = task.photos[photoIndex];
      
      // 检查照片是否已上传
      if (photo.imagePath == null || photo.imagePath!.isEmpty) {
        throw Exception('照片尚未上传，无法开始识别');
      }

      // 更新识别状态
      task.photos[photoIndex] = photo.copyWith(
        recognitionStatus: RecognitionStatus.processing,
        recognitionStartTime: DateTime.now(),
        isRecognitionCompleted: false,
      );

      AppLogger.info('✅ 照片识别已开始: ${photo.label}', tag: 'RecognitionMgmt');
    } catch (e) {
      AppLogger.error('❌ 开始照片识别失败: $e', tag: 'RecognitionMgmt');
      rethrow;
    }
  }

  /// 取消照片识别
  Future<void> cancelRecognition(TaskModel task, String photoId) async {
    try {
      AppLogger.info('❌ 取消照片识别: $photoId', tag: 'RecognitionMgmt');
      
      final photoIndex = task.photos.indexWhere((p) => p.id == photoId);
      if (photoIndex == -1) {
        throw Exception('照片不存在: $photoId');
      }

      final photo = task.photos[photoIndex];

      // 更新识别状态
      task.photos[photoIndex] = photo.copyWith(
        recognitionStatus: RecognitionStatus.pending,
        recognitionStartTime: null,
        recognitionEndTime: null,
        isRecognitionCompleted: false,
        recognitionResult: null,
      );

      AppLogger.info('✅ 照片识别已取消: ${photo.label}', tag: 'RecognitionMgmt');
    } catch (e) {
      AppLogger.error('❌ 取消照片识别失败: $e', tag: 'RecognitionMgmt');
      rethrow;
    }
  }

  /// 重置照片识别状态
  Future<void> resetRecognition(TaskModel task, String photoId) async {
    try {
      AppLogger.info('🔄 重置照片识别: $photoId', tag: 'RecognitionMgmt');
      
      final photoIndex = task.photos.indexWhere((p) => p.id == photoId);
      if (photoIndex == -1) {
        throw Exception('照片不存在: $photoId');
      }

      final photo = task.photos[photoIndex];

      // 重置识别状态和结果
      task.photos[photoIndex] = photo.copyWith(
        recognitionStatus: RecognitionStatus.pending,
        recognitionStartTime: null,
        recognitionEndTime: null,
        isRecognitionCompleted: false,
        recognitionResult: null,
        isVerified: false,
        matchedBatchIds: null,
        matchedProductCode: null,
      );

      AppLogger.info('✅ 照片识别状态已重置: ${photo.label}', tag: 'RecognitionMgmt');
    } catch (e) {
      AppLogger.error('❌ 重置照片识别失败: $e', tag: 'RecognitionMgmt');
      rethrow;
    }
  }

  /// 更新识别结果
  Future<void> updateRecognitionResult(
    TaskModel task,
    String photoId,
    RecognitionResult result,
  ) async {
    try {
      AppLogger.info('🔍 更新识别结果: $photoId', tag: 'RecognitionMgmt');
      
      final photoIndex = task.photos.indexWhere((p) => p.id == photoId);
      if (photoIndex == -1) {
        throw Exception('照片不存在: $photoId');
      }

      final photo = task.photos[photoIndex];

      // 更新识别结果和状态
      task.photos[photoIndex] = photo.copyWith(
        recognitionResult: result,
        recognitionStatus: RecognitionStatus.completed,
        recognitionEndTime: DateTime.now(),
        isRecognitionCompleted: true,
        isVerified: result.matchesPreset,
        matchedProductCode: result.extractedProductCode,
      );

      // 更新批次识别统计
      _updateBatchRecognitionStats(task, photo.label);

      AppLogger.info('✅ 识别结果更新成功: ${photo.label}', tag: 'RecognitionMgmt');
    } catch (e) {
      AppLogger.error('❌ 更新识别结果失败: $e', tag: 'RecognitionMgmt');
      rethrow;
    }
  }

  /// 批量开始识别
  Future<void> startBatchRecognition(TaskModel task, List<String> photoIds) async {
    try {
      AppLogger.info('🔍 批量开始识别: ${photoIds.length}张照片', tag: 'RecognitionMgmt');
      
      for (final photoId in photoIds) {
        await startRecognition(task, photoId);
      }
      
      AppLogger.info('✅ 批量识别启动完成', tag: 'RecognitionMgmt');
    } catch (e) {
      AppLogger.error('❌ 批量开始识别失败: $e', tag: 'RecognitionMgmt');
      rethrow;
    }
  }

  /// 批量取消识别
  Future<void> cancelBatchRecognition(TaskModel task, List<String> photoIds) async {
    try {
      AppLogger.info('❌ 批量取消识别: ${photoIds.length}张照片', tag: 'RecognitionMgmt');
      
      for (final photoId in photoIds) {
        await cancelRecognition(task, photoId);
      }
      
      AppLogger.info('✅ 批量取消识别完成', tag: 'RecognitionMgmt');
    } catch (e) {
      AppLogger.error('❌ 批量取消识别失败: $e', tag: 'RecognitionMgmt');
      rethrow;
    }
  }

  /// 获取识别进度统计
  Map<String, dynamic> getRecognitionProgress(TaskModel task) {
    try {
      final totalPhotos = task.photos.length;
      final pendingPhotos = task.photos
          .where((p) => p.recognitionStatus == RecognitionStatus.pending)
          .length;
      final processingPhotos = task.photos
          .where((p) => p.recognitionStatus == RecognitionStatus.processing)
          .length;
      final completedPhotos = task.photos
          .where((p) => p.recognitionStatus == RecognitionStatus.completed)
          .length;
      final verifiedPhotos = task.photos
          .where((p) => p.isVerified)
          .length;

      return {
        'total': totalPhotos,
        'pending': pendingPhotos,
        'processing': processingPhotos,
        'completed': completedPhotos,
        'verified': verifiedPhotos,
        'completionRate': totalPhotos > 0 ? (completedPhotos / totalPhotos * 100) : 0.0,
        'verificationRate': totalPhotos > 0 ? (verifiedPhotos / totalPhotos * 100) : 0.0,
      };
    } catch (e) {
      AppLogger.error('❌ 获取识别进度失败: $e', tag: 'RecognitionMgmt');
      return {
        'total': 0,
        'pending': 0,
        'processing': 0,
        'completed': 0,
        'verified': 0,
        'completionRate': 0.0,
        'verificationRate': 0.0,
      };
    }
  }

  /// 检查是否可以开始识别
  bool canStartRecognition(TaskModel task, String photoId) {
    try {
      final photo = task.photos.firstWhere(
        (p) => p.id == photoId,
        orElse: () => throw Exception('照片不存在'),
      );

      // 检查照片是否已上传
      if (photo.imagePath == null || photo.imagePath!.isEmpty) {
        return false;
      }

      // 检查是否需要识别
      if (!photo.needRecognition) {
        return false;
      }

      // 检查当前状态
      return photo.recognitionStatus == RecognitionStatus.pending;
    } catch (e) {
      AppLogger.error('❌ 检查识别条件失败: $e', tag: 'RecognitionMgmt');
      return false;
    }
  }

  /// 获取识别错误信息
  List<String> getRecognitionErrors(TaskModel task) {
    final errors = <String>[];
    
    try {
      for (final photo in task.photos) {
        if (photo.needRecognition && photo.imagePath != null) {
          if (photo.recognitionStatus == RecognitionStatus.pending) {
            errors.add('照片 ${photo.label} 尚未开始识别');
          } else if (photo.recognitionStatus == RecognitionStatus.processing) {
            // 检查是否超时
            if (photo.recognitionStartTime != null) {
              final duration = DateTime.now().difference(photo.recognitionStartTime!);
              if (duration.inMinutes > 5) {
                errors.add('照片 ${photo.label} 识别超时');
              }
            }
          } else if (photo.recognitionStatus == RecognitionStatus.completed && !photo.isVerified) {
            errors.add('照片 ${photo.label} 识别失败');
          }
        }
      }
    } catch (e) {
      AppLogger.error('❌ 获取识别错误信息失败: $e', tag: 'RecognitionMgmt');
      errors.add('获取识别错误信息失败: $e');
    }
    
    return errors;
  }

  /// 私有方法：更新批次识别统计
  void _updateBatchRecognitionStats(TaskModel task, String photoLabel) {
    try {
      // 根据照片标签更新对应批次的统计
      for (final batch in task.batches) {
        // 更新识别数量
        batch.recognizedQuantity += 1;

        // 检查批次是否完成
        _checkBatchCompletion(batch);
      }

      AppLogger.info('📊 批次识别统计已更新: $photoLabel', tag: 'RecognitionMgmt');
    } catch (e) {
      AppLogger.error('❌ 更新批次识别统计失败: $e', tag: 'RecognitionMgmt');
    }
  }

  /// 私有方法：检查批次是否完成
  void _checkBatchCompletion(BatchInfo batch) {
    try {
      // 使用BatchInfo的内置完成逻辑
      final isCompleted = batch.isCompleted;

      AppLogger.info('📊 批次完成检查: ${batch.batchNumber} - ${isCompleted ? '已完成' : '未完成'}', tag: 'RecognitionMgmt');
    } catch (e) {
      AppLogger.error('❌ 检查批次完成状态失败: $e', tag: 'RecognitionMgmt');
    }
  }
}
