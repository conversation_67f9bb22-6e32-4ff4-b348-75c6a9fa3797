# 冗余文件清理计划

## 🎯 清理目标
基于新的架构升级，清理不再使用的文件、API和Provider，确保代码库整洁。

## 📋 冗余文件分析

### 1. 已被替代的服务文件
这些文件已被新的统一服务替代：

#### 错误处理相关
- ❌ `lib/services/error_handler_service.dart` → ✅ `lib/services/global_error_handler.dart`
- ❌ `lib/services/logging_service.dart` → ✅ `lib/utils/app_logger.dart`

#### 性能监控相关
- ❌ `lib/services/performance_optimizer.dart` → ✅ `lib/services/performance_manager.dart`
- ❌ `lib/services/enhanced_performance_monitor.dart` → ✅ `lib/services/performance_manager.dart`
- ❌ `lib/services/performance_monitor_service.dart` → ✅ `lib/services/performance_manager.dart`
- ❌ `lib/services/memory_optimization_service.dart` → ✅ `lib/services/resource_manager.dart`

#### 缓存相关
- ❌ `lib/services/enhanced_cache_service.dart` → ✅ 集成到新架构中
- ❌ `lib/services/performance_cache_service.dart` → ✅ 集成到新架构中

#### 安全相关（可能重复）
- 🔍 `lib/services/app_security_service.dart` vs `lib/services/data_security_manager.dart`
- 🔍 `lib/services/strict_security_service.dart` vs `lib/services/data_security_manager.dart`
- 🔍 `lib/services/secure_key_manager.dart` vs `lib/services/encryption_service.dart`

### 2. 重复的工具文件
- ❌ `lib/utils/memory_optimizer.dart` → ✅ `lib/services/resource_manager.dart`
- 🔍 多个导航辅助文件需要合并

### 3. 未使用的页面和组件
- 🔍 检查是否有未引用的页面文件
- 🔍 检查是否有未使用的Widget

### 4. 旧的数据源文件
- 🔍 `lib/repositories/shared_prefs_task_data_source.dart` 是否还需要

## 🚀 清理步骤

### 阶段1：安全移除明确冗余的文件
1. 备份当前代码
2. 移除已确认被替代的服务文件
3. 更新所有引用
4. 运行测试确保功能正常

### 阶段2：合并重复功能
1. 分析重复的安全服务
2. 合并导航辅助工具
3. 统一缓存管理

### 阶段3：清理未使用的代码
1. 扫描未引用的文件
2. 移除死代码
3. 优化导入语句

## 📊 预期效果
- 减少代码库大小约20-30%
- 提高代码可维护性
- 减少编译时间
- 降低内存占用

## ⚠️ 注意事项
1. 每次移除文件前都要检查引用
2. 保持向后兼容性
3. 确保所有测试通过
4. 保留重要的业务逻辑

## 🔄 迁移映射

### 错误处理迁移
```dart
// 旧代码
import 'package:loadguard/services/error_handler_service.dart';
ErrorHandlerService().handleError(error);

// 新代码
import 'package:loadguard/services/global_error_handler.dart';
ref.read(globalErrorHandlerProvider).handleException(error);
```

### 性能监控迁移
```dart
// 旧代码
import 'package:loadguard/services/performance_optimizer.dart';
PerformanceOptimizer().recordMetric(name, value);

// 新代码
import 'package:loadguard/services/performance_manager.dart';
ref.read(performanceManagerProvider).recordMetric(name, value);
```

### 资源管理迁移
```dart
// 旧代码
import 'package:loadguard/services/memory_optimization_service.dart';
MemoryOptimizationService().cleanup();

// 新代码
import 'package:loadguard/services/resource_manager.dart';
ref.read(resourceManagerProvider).cleanupAllResources();
```

## 📝 清理检查清单
- [ ] 移除冗余服务文件
- [ ] 更新所有import语句
- [ ] 合并重复功能
- [ ] 清理未使用的Provider
- [ ] 移除死代码
- [ ] 优化文件结构
- [ ] 运行所有测试
- [ ] 验证功能完整性
- [ ] 更新文档
- [ ] 提交清理后的代码
