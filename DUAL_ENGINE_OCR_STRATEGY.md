# 双引擎OCR识别策略

## 概述

LoadGuard智能装车监管系统现在支持双引擎OCR识别策略，通过结合Google MLKit和百度PaddleOCR两种识别引擎，根据不同场景智能选择最优的识别引擎，从而提高整体识别准确率。

## 引擎特性对比

| 特性 | MLKit | PaddleOCR |
|------|-------|-----------|
| 准确率 | 88% | 95% |
| 处理速度 | 500ms | 1800ms |
| 内存占用 | 40MB | 120MB |
| 支持二维码 | 是 | 否 |
| 多语言支持 | 中文、英文 | 80+语言 |
| 适用场景 | 快速识别、通用场景 | 复杂背景、高精度要求 |

## 智能引擎选择策略

系统根据以下因素智能选择最优识别引擎：

1. **图像复杂度**：
   - 低复杂度：优先选择MLKit
   - 高复杂度：优先选择PaddleOCR

2. **背景颜色**：
   - 蓝色、绿色、红色、紫色背景：加分给PaddleOCR
   - 深色背景：强烈加分给PaddleOCR
   - 其他背景：加分给MLKit

3. **反光程度**：
   - 高反光：加分给PaddleOCR
   - 低反光：加分给MLKit

4. **识别模式**：
   - 快速模式：强烈加分给MLKit
   - 标准模式：均衡考虑
   - 高精度模式：强烈加分给PaddleOCR

## 降级策略

当PaddleOCR识别失败时，系统会自动降级到MLKit引擎，确保识别过程不会中断。

## 使用方法

在调用识别功能时，系统会自动根据图像特征和识别模式选择最优引擎：

```dart
final results = await AdaptiveRecognitionService.instance.recognizeAdaptive(
  imagePath: imagePath,
  mode: RecognitionMode.standard, // 或fast、precision
  presetProductCode: productCode,
  presetBatchNumber: batchNumber,
  allBatches: batches,
  onProgress: (progress, status) {
    // 处理进度更新
  },
);
```

## 预期效果

通过双引擎识别策略，我们预期能够将整体识别准确率从85%提升到92%以上，同时保持合理的处理速度。