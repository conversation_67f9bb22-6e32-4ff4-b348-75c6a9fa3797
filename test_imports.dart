// 简单的导入测试脚本
// 用于验证包名是否正确，不依赖riverpod_generator

import 'package:smartguard/utils/app_logger.dart';
import 'package:smartguard/models/task_model.dart';
import 'package:smartguard/services/task_service.dart';
import 'package:smartguard/core/providers/app_providers.dart';

void main() {
  print('✅ 包名导入测试成功！');
  print('📦 smartguard包可以正常导入');
  
  // 简单的类型检查
  print('🔍 检查类型定义...');
  
  try {
    // 检查AppLogger是否可用
    AppLogger.info('测试日志');
    print('✅ AppLogger 可用');
  } catch (e) {
    print('❌ AppLogger 错误: $e');
  }
  
  print('🎉 导入测试完成！');
}
