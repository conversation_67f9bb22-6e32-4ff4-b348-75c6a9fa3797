# PowerShell脚本：批量更新包名从 loadguard 到 smartguard
# 用法: .\scripts\update_package_name.ps1

Write-Host "🔄 开始批量更新包名..." -ForegroundColor Green

# 定义要搜索的目录
$directories = @("lib", "test")

# 定义要替换的模式
$oldPackage = "package:loadguard/"
$newPackage = "package:smartguard/"

# 统计变量
$totalFiles = 0
$updatedFiles = 0
$totalReplacements = 0

foreach ($dir in $directories) {
    if (Test-Path $dir) {
        Write-Host "📁 处理目录: $dir" -ForegroundColor Yellow
        
        # 获取所有.dart文件
        $dartFiles = Get-ChildItem -Path $dir -Recurse -Filter "*.dart"
        
        foreach ($file in $dartFiles) {
            $totalFiles++
            $content = Get-Content $file.FullName -Raw -Encoding UTF8
            
            if ($content -match [regex]::Escape($oldPackage)) {
                Write-Host "  🔧 更新文件: $($file.Name)" -ForegroundColor Cyan
                
                # 计算替换次数
                $matches = [regex]::Matches($content, [regex]::Escape($oldPackage))
                $replacements = $matches.Count
                $totalReplacements += $replacements
                
                # 执行替换
                $newContent = $content -replace [regex]::Escape($oldPackage), $newPackage
                
                # 写回文件
                Set-Content -Path $file.FullName -Value $newContent -Encoding UTF8
                $updatedFiles++
                
                Write-Host "    ✅ 替换了 $replacements 处引用" -ForegroundColor Green
            }
        }
    } else {
        Write-Host "⚠️  目录不存在: $dir" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "📊 更新统计:" -ForegroundColor Green
Write-Host "  总文件数: $totalFiles" -ForegroundColor White
Write-Host "  更新文件数: $updatedFiles" -ForegroundColor White
Write-Host "  总替换次数: $totalReplacements" -ForegroundColor White

if ($updatedFiles -gt 0) {
    Write-Host ""
    Write-Host "🎉 包名更新完成！" -ForegroundColor Green
    Write-Host "💡 建议运行以下命令验证更新:" -ForegroundColor Yellow
    Write-Host "   flutter clean" -ForegroundColor Cyan
    Write-Host "   flutter pub get" -ForegroundColor Cyan
    Write-Host "   flutter analyze" -ForegroundColor Cyan
} else {
    Write-Host ""
    Write-Host "ℹ️  没有找到需要更新的文件" -ForegroundColor Blue
}

Write-Host ""
Write-Host "✨ 脚本执行完成！" -ForegroundColor Green
