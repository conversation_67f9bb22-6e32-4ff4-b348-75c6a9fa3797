name: loadguard
description: "智运卫士 - 智能装车监管系统 | SmartGuard Logistics - Intelligent Loading Supervision System"
publish_to: 'none'

version: 2.0.0+200

environment:
  sdk: '>=3.8.0 <4.0.0'
  flutter: '>=3.32.0'

dependencies:
  flutter:
    sdk: flutter

  # 🎯 状态管理 - 最新稳定版本
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1

  # 🎨 UI组件
  cupertino_icons: ^1.0.8

  # 🧭 路由系统 - 稳定版本
  go_router: ^16.0.0

  # 💾 数据存储 - 稳定版本
  shared_preferences: ^2.2.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.3
  path_provider_android: ^2.2.1

  # 🔐 权限和设备信息
  permission_handler: ^12.0.1
  device_info_plus: ^11.5.0
  package_info_plus: ^8.0.0

  # 📷 图像处理
  image_picker: ^1.1.2
  image: ^4.2.0

  # 🌐 网络请求
  http: ^1.2.1
  dio: ^5.5.0
  connectivity_plus: ^6.0.3

  # 🔒 加密和安全
  crypto: ^3.0.3
  encrypt: ^5.0.3

  # 🤖 ML Kit - 严格保持0.15.0版本
  google_mlkit_text_recognition: ^0.15.0
  google_mlkit_barcode_scanning: ^0.14.1
  google_mlkit_commons: ^0.11.0

  # 📄 PDF和文档
  pdf: ^3.11.0
  printing: ^5.14.2

  # 📊 图表和数据可视化
  fl_chart: ^1.0.0
  qr_flutter: ^4.1.0

  # 🌍 国际化
  intl: ^0.20.2

  # 🔧 工具库
  # json_annotation: ^4.8.1  # 已移除：不使用代码生成
  async: ^2.11.0
  collection: ^1.17.2
  share_plus: ^11.0.0

  # 🎯 数据类生成 - 已移除，使用手动实现
  # freezed_annotation: ^3.1.0  # 已移除：手动实现数据类

  # 📸 OCR相关
  paddle_ocr: ^0.0.8
  paddle_ocr_plus: ^0.0.7

dev_dependencies:
  flutter_test:
    sdk: flutter

  # 🔧 代码生成工具 - 重新启用
  build_runner: ^2.4.13
  hive_generator: ^2.0.1
  riverpod_generator: ^2.6.3
  riverpod_lint: ^2.6.3

  # 📝 代码检查 - 最新版本
  flutter_lints: ^6.0.0

  # 🧪 测试工具
  # mockito: ^5.4.5  # 暂时注释，解决依赖冲突

# Flutter配置
flutter:
  uses-material-design: true
  
  # 资源文件
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/
    - assets/config/
    - assets/models/
    - assets/ocr/
  
  # 字体配置
  fonts:
    - family: NotoSansSC
      fonts:
        - asset: assets/fonts/NotoSansSC-Regular.ttf
        - asset: assets/fonts/NotoSansSC-Bold.ttf
          weight: 700
    - family: SourceHanSans
      fonts:
        - asset: assets/fonts/SourceHanSans-Normal.ttc