/// 🎯 任务管理服务 - 重构版
///
/// 作为各个专职服务的协调器，不再包含具体的业务逻辑
/// 所有具体功能都委托给相应的专职服务
///
/// ⚠️ **过渡期使用** - 最终目标是完全迁移到 TaskNotifier 和专职服务
library task_service;


import 'dart:async';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/utils/app_logger.dart';
import 'package:loadguard/repositories/task_repository.dart';
import 'package:loadguard/repositories/task_repository_impl.dart';
import 'package:loadguard/services/photo_management_service.dart';
import 'package:loadguard/services/recognition_management_service.dart';
import 'package:loadguard/services/task_creation_service.dart';
import 'package:loadguard/services/task_persistence_service.dart';

/// 任务管理服务 - 重构版
class TaskService {
  static const String _tag = 'TaskService';
  
  final TaskRepository _repository;
  
  // 🆕 专职服务
  late final PhotoManagementService _photoService;
  late final RecognitionManagementService _recognitionService;
  late final TaskCreationService _creationService;
  late final TaskPersistenceService _persistenceService;

  TaskModel? _currentTask;
  List<TaskModel> _tasks = [];

  // 🚀 批量更新机制
  Timer? _updateTimer;
  bool _pendingUpdate = false;
  static const Duration _batchUpdateDelay = Duration(milliseconds: 100);

  // 🚀 Repository监听器
  StreamSubscription<List<TaskModel>>? _tasksSubscription;
  StreamSubscription<TaskModel?>? _currentTaskSubscription;

  TaskModel? get currentTask => _currentTask;
  List<TaskModel> get tasks => _tasks;

  /// 构造函数 - 初始化专职服务
  TaskService({TaskRepository? repository})
      : _repository = repository ?? TaskRepositoryImpl() {
    // 初始化专职服务
    _photoService = PhotoManagementService();
    _recognitionService = RecognitionManagementService();
    _creationService = TaskCreationService();
    _persistenceService = TaskPersistenceService(repository: _repository);
  }

  /// 初始化服务
  Future<void> initialize() async {
    try {
      AppLogger.info('🚀 初始化任务服务...', tag: _tag);

      // 初始化Repository
      await _repository.initialize();

      // 设置Repository数据监听
      _setupRepositoryListeners();

      // 加载初始数据
      await _loadTasks();

      AppLogger.info('✅ 任务服务初始化完成', tag: _tag);
    } catch (e) {
      AppLogger.error('❌ 任务服务初始化失败: $e', tag: _tag);
      rethrow;
    }
  }

  /// 设置Repository数据监听
  void _setupRepositoryListeners() {
    // 监听任务列表变化
    if (_repository is TaskRepositoryImpl) {
      _tasksSubscription = _repository.watchTasks().listen((tasks) {
        _tasks = tasks;
        _scheduleUpdate();
      });
      
      _currentTaskSubscription = _repository.watchCurrentTask().listen((task) {
        _currentTask = task;
        _scheduleUpdate();
      });
    }
  }

  /// 🚀 批量更新机制
  void _scheduleUpdate() {
    if (_pendingUpdate) return;

    _pendingUpdate = true;
    _updateTimer?.cancel();
    _updateTimer = Timer(_batchUpdateDelay, () {
      _pendingUpdate = false;
      AppLogger.debug('📝 TaskService批量更新完成', tag: _tag);
    });
  }

  /// 加载任务数据
  Future<void> _loadTasks() async {
    try {
      // 从Repository加载任务
      _tasks = await _repository.getAllTasks();
      
      // 加载当前任务
      _currentTask = await _repository.getCurrentTask();
      
      // 🚀 使用批量更新机制
      _scheduleUpdate();
      
      AppLogger.info('📚 任务加载完成: ${_tasks.length}个任务', tag: _tag);
    } catch (e) {
      AppLogger.error('❌ 任务加载失败: $e', tag: _tag);
      _tasks = [];
      _currentTask = null;
      _scheduleUpdate();
    }
  }

  /// 创建单批次任务
  Future<TaskModel> createTask({
    required String template,
    required String productCode,
    required String batchNumber,
    required int quantity,
    List<String> participants = const [],
  }) async {
    AppLogger.info('📝 创建单批次任务: $template', tag: _tag);
    
    // 委托给专职服务
    final task = await _creationService.createSingleBatchTask(
      template: template,
      productCode: productCode,
      batchNumber: batchNumber,
      quantity: quantity,
      participants: participants,
    );

    // 设置为当前任务
    _currentTask = task;
    _tasks.insert(0, task);
    
    // 保存任务
    await _persistenceService.saveTask(task);
    
    _scheduleUpdate();
    return task;
  }

  /// 创建混装任务
  Future<TaskModel> createMixedTask({
    required String template,
    required List<BatchInfo> batches,
    List<String> participants = const [],
  }) async {
    AppLogger.info('📝 创建混装任务: $template', tag: _tag);
    
    // 委托给专职服务
    final task = await _creationService.createMixedTask(
      template: template,
      batches: batches,
      participants: participants,
    );

    // 设置为当前任务
    _currentTask = task;
    _tasks.insert(0, task);
    
    // 保存任务
    await _persistenceService.saveTask(task);
    
    _scheduleUpdate();
    return task;
  }

  /// 更新照片
  Future<void> updatePhoto(String photoId, String imagePath) async {
    AppLogger.info('📸 更新照片: $photoId', tag: _tag);
    
    if (_currentTask == null) return;
    
    // 委托给专职服务
    await _photoService.updatePhotoPath(_currentTask!, photoId, imagePath);

    // 保存任务
    await _persistenceService.saveTask(_currentTask!);
    _scheduleUpdate();
  }

  /// 更新照片识别结果
  Future<void> updatePhotoRecognitionResult(
    String taskId,
    String photoId,
    RecognitionResult result,
  ) async {
    AppLogger.info('🔍 更新照片识别结果: $photoId', tag: _tag);

    if (_currentTask == null) return;

    // 委托给专职服务
    await _recognitionService.updateRecognitionResult(_currentTask!, photoId, result);

    // 保存任务
    await _persistenceService.saveTask(_currentTask!);

    // 刷新数据
    await _loadTasks();
  }

  /// 设置当前任务
  Future<void> setCurrentTask(TaskModel? task) async {
    try {
      // 使用Repository设置当前任务
      await _repository.setCurrentTask(task);
      
      // 更新本地缓存
      _currentTask = task;
      
      _scheduleUpdate();
      
      AppLogger.info('📌 当前任务已设置: ${task?.id ?? 'null'}', tag: _tag);
    } catch (e) {
      AppLogger.error('❌ 设置当前任务失败: $e', tag: _tag);
      rethrow;
    }
  }

  /// 删除任务
  Future<void> deleteTask(String taskId) async {
    try {
      // 使用Repository删除任务
      await _repository.deleteTask(taskId);
      
      // 更新本地缓存
      _tasks.removeWhere((task) => task.id == taskId);
      
      // 如果删除的是当前任务，清除当前任务
      if (_currentTask?.id == taskId) {
        _currentTask = null;
      }
      
      _scheduleUpdate();
      
      AppLogger.info('🗑️ 删除任务成功: $taskId', tag: _tag);
    } catch (e) {
      AppLogger.error('❌ 删除任务失败: $taskId - $e', tag: _tag);
      rethrow;
    }
  }

  /// 获取任务通过ID
  TaskModel? getTaskById(String taskId) {
    try {
      return _tasks.firstWhere((task) => task.id == taskId);
    } catch (e) {
      return null;
    }
  }

  /// 刷新任务数据
  Future<void> refreshData() async {
    try {
      await _loadTasks();
      AppLogger.info('✅ 任务数据刷新完成，共${_tasks.length}个任务', tag: _tag);
    } catch (e) {
      AppLogger.error('❌ 刷新任务数据失败: $e', tag: _tag);
      rethrow;
    }
  }

  /// ⚠️ **已废弃** - 请使用 TaskNotifier.updateTask() 替代
  @Deprecated('使用 TaskNotifier.updateTask() 替代')
  Future<void> updateTask(TaskModel task) async {
    AppLogger.warning('⚠️ 使用已废弃的TaskService.updateTask，请迁移到TaskNotifier', tag: _tag);

    // 查找并更新任务列表中的任务
    final index = _tasks.indexWhere((t) => t.id == task.id);
    if (index != -1) {
      _tasks[index] = task;
    }

    // 如果是当前任务，也更新当前任务引用
    if (_currentTask?.id == task.id) {
      _currentTask = task;
    }

    // 保存到本地存储
    await _persistenceService.saveTask(task);

    // 异步通知监听器
    _scheduleUpdate();
  }

  /// ⚠️ **已废弃** - 请使用 TaskNotifier.updatePhotoRecognitionResult() 替代
  @Deprecated('使用 TaskNotifier.updatePhotoRecognitionResult() 替代')
  Future<void> updateRecognitionResult(String photoId, RecognitionResult result) async {
    AppLogger.warning('⚠️ 使用已废弃的TaskService.updateRecognitionResult，请迁移到TaskNotifier', tag: _tag);

    if (_currentTask == null) return;

    // 委托给专职服务
    await _recognitionService.updateRecognitionResult(_currentTask!, photoId, result);

    // 保存任务
    await _persistenceService.saveTask(_currentTask!);
    _scheduleUpdate();
  }

  /// ⚠️ **已废弃** - 请使用 TaskNotifier.completeTask() 替代
  @Deprecated('使用 TaskNotifier.completeTask() 替代')
  Future<void> completeTask() async {
    AppLogger.warning('⚠️ 使用已废弃的TaskService.completeTask，请迁移到TaskNotifier', tag: _tag);

    if (_currentTask == null) return;

    // 标记任务为完成
    _currentTask = _currentTask!.copyWith(
      completedAt: DateTime.now(),
    );

    // 保存任务
    await _persistenceService.saveTask(_currentTask!);
    _scheduleUpdate();
  }

  /// ⚠️ **已废弃** - 请使用 TaskNotifier.saveTask() 替代
  @Deprecated('使用 TaskNotifier.saveTask() 替代')
  Future<void> saveTaskDirectly(TaskModel task) async {
    AppLogger.warning('⚠️ 使用已废弃的TaskService.saveTaskDirectly，请迁移到TaskNotifier', tag: _tag);

    // 委托给专职服务
    await _persistenceService.saveTask(task);

    // 更新本地缓存
    final existingIndex = _tasks.indexWhere((t) => t.id == task.id);
    if (existingIndex >= 0) {
      _tasks[existingIndex] = task;
    } else {
      _tasks.insert(0, task);
    }

    // 如果是当前任务，也更新当前任务引用
    if (_currentTask?.id == task.id) {
      _currentTask = task;
    }

    _scheduleUpdate();
  }

  /// ⚠️ **已废弃** - 请使用 TaskNotifier.updatePhotoOptimistic() 替代
  @Deprecated('使用 TaskNotifier.updatePhotoOptimistic() 替代')
  Future<void> updatePhotoResult(String taskId, String photoId, RecognitionResult result) async {
    AppLogger.warning('⚠️ 使用已废弃的TaskService.updatePhotoResult，请迁移到TaskNotifier', tag: _tag);

    if (_currentTask == null) return;

    // 委托给专职服务
    await _recognitionService.updateRecognitionResult(_currentTask!, photoId, result);

    // 保存任务
    await _persistenceService.saveTask(_currentTask!);
    _scheduleUpdate();
  }

  /// ⚠️ **已废弃** - 请使用 TaskNotifier.updatePhotoOptimistic() 替代
  @Deprecated('使用 TaskNotifier.updatePhotoOptimistic() 替代')
  Future<void> updatePhotoDirectly(String photoId, String imagePath) async {
    AppLogger.warning('⚠️ 使用已废弃的TaskService.updatePhotoDirectly，请迁移到TaskNotifier', tag: _tag);

    if (_currentTask == null) return;

    // 委托给专职服务
    await _photoService.updatePhotoPath(_currentTask!, photoId, imagePath);

    // 保存任务
    await _persistenceService.saveTask(_currentTask!);
    _scheduleUpdate();
  }



  /// 清理资源
  void dispose() {
    // 🚀 清理批量更新Timer
    _updateTimer?.cancel();

    // 🚀 清理Repository监听器
    _tasksSubscription?.cancel();
    _currentTaskSubscription?.cancel();

    // 🚀 清理Repository资源
    if (_repository is TaskRepositoryImpl) {
      _repository.dispose();
    }

    AppLogger.info('🧹 TaskService资源已清理', tag: _tag);
  }
}
