import 'dart:io';
import 'dart:convert';

/// 修复项目中的UTF-8编码问题
void main() async {
  print('🔧 开始修复UTF-8编码问题...');
  
  final problematicFiles = <String>[];
  final fixedFiles = <String>[];
  
  // 需要检查的文件类型
  final extensions = ['.dart', '.yaml', '.json', '.md', '.txt'];
  
  // 递归扫描lib目录
  await _scanDirectory(Directory('lib'), extensions, problematicFiles, fixedFiles);
  
  // 检查根目录的配置文件
  final rootFiles = ['pubspec.yaml', 'analysis_options.yaml', 'README.md'];
  for (final fileName in rootFiles) {
    final file = File(fileName);
    if (await file.exists()) {
      await _checkAndFixFile(file, problematicFiles, fixedFiles);
    }
  }
  
  // 输出结果
  print('\n📊 修复结果:');
  print('- 检查的文件: ${problematicFiles.length + fixedFiles.length}');
  print('- 修复的文件: ${fixedFiles.length}');
  print('- 问题文件: ${problematicFiles.length}');
  
  if (fixedFiles.isNotEmpty) {
    print('\n✅ 已修复的文件:');
    for (final file in fixedFiles) {
      print('  - $file');
    }
  }
  
  if (problematicFiles.isNotEmpty) {
    print('\n⚠️ 仍有问题的文件:');
    for (final file in problematicFiles) {
      print('  - $file');
    }
  }
  
  print('\n🎉 编码修复完成！');
}

/// 递归扫描目录
Future<void> _scanDirectory(
  Directory dir, 
  List<String> extensions,
  List<String> problematicFiles,
  List<String> fixedFiles,
) async {
  try {
    await for (final entity in dir.list(recursive: true)) {
      if (entity is File) {
        final extension = _getFileExtension(entity.path);
        if (extensions.contains(extension)) {
          await _checkAndFixFile(entity, problematicFiles, fixedFiles);
        }
      }
    }
  } catch (e) {
    print('⚠️ 扫描目录失败: ${dir.path} - $e');
  }
}

/// 检查并修复单个文件
Future<void> _checkAndFixFile(
  File file,
  List<String> problematicFiles,
  List<String> fixedFiles,
) async {
  try {
    // 读取原始字节
    final bytes = await file.readAsBytes();
    
    // 检查是否有BOM
    bool hasBOM = false;
    List<int> cleanBytes = bytes;
    
    if (bytes.length >= 3 && 
        bytes[0] == 0xEF && 
        bytes[1] == 0xBB && 
        bytes[2] == 0xBF) {
      hasBOM = true;
      cleanBytes = bytes.sublist(3); // 移除BOM
    }
    
    // 尝试解码为UTF-8
    String content;
    bool needsFix = false;
    
    try {
      content = utf8.decode(cleanBytes, allowMalformed: false);
    } catch (e) {
      // 如果解码失败，尝试修复
      content = utf8.decode(cleanBytes, allowMalformed: true);
      needsFix = true;
    }
    
    // 检查是否包含无效字符
    if (content.contains('\uFFFD')) {
      needsFix = true;
    }
    
    // 如果需要修复或有BOM
    if (needsFix || hasBOM) {
      // 清理内容
      content = content.replaceAll('\uFFFD', ''); // 移除替换字符
      content = content.replaceAll(RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]'), ''); // 移除控制字符
      
      // 重新写入文件（无BOM的UTF-8）
      await file.writeAsString(content, encoding: utf8);
      fixedFiles.add(file.path);
      print('✅ 修复: ${file.path}');
    }
    
  } catch (e) {
    problematicFiles.add(file.path);
    print('❌ 无法修复: ${file.path} - $e');
  }
}

/// 获取文件扩展名
String _getFileExtension(String path) {
  final lastDot = path.lastIndexOf('.');
  if (lastDot == -1) return '';
  return path.substring(lastDot);
}
