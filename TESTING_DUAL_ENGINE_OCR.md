# 双引擎OCR识别策略测试指南

## 概述

本文档说明如何测试LoadGuard项目中的双引擎OCR识别策略，包括MLKit和PaddleOCR引擎的集成测试。

## 测试环境准备

### 1. 模型文件准备

确保以下模型文件已正确部署：

#### Android
- [android/app/src/main/assets/models/ch_PP-OCRv4_det_infer.nb](file:///d:/weishi/android/app/src/main/assets/models/ch_PP-OCRv4_det_infer.nb)
- [android/app/src/main/assets/models/ch_PP-OCRv4_rec_infer.nb](file:///d:/weishi/android/app/src/main/assets/models/ch_PP-OCRv4_rec_infer.nb)
- [android/app/src/main/assets/models/ch_ppocr_mobile_v2.0_cls_infer.nb](file:///d:/weishi/android/app/src/main/assets/models/ch_ppocr_mobile_v2.0_cls_infer.nb)
- [android/app/src/main/assets/models/ppocr_keys_v1.txt](file:///d:/weishi/android/app/src/main/assets/models/ppocr_keys_v1.txt)

#### iOS
- ios/Runner/models/ch_PP-OCRv4_det_infer.nb
- ios/Runner/models/ch_PP-OCRv4_rec_infer.nb
- ios/Runner/models/ch_ppocr_mobile_v2.0_cls_infer.nb
- ios/Runner/models/ppocr_keys_v1.txt

### 2. 依赖库检查

确保已添加必要的依赖库：

#### Android
- Paddle-Lite库
- OpenCV库

#### iOS
- PaddleOCR iOS Framework

## 测试用例

### 1. 引擎初始化测试

验证两个引擎都能正确初始化：

```dart
// 测试MLKit初始化
final mlkitEngine = _MLKitEngineAdapter(mlkitService);
expect(mlkitEngine.isInitialized, true);

// 测试PaddleOCR初始化
final paddleEngine = PaddleOcrEngine();
await paddleEngine.initialize();
expect(paddleEngine.isInitialized, true);
```

### 2. 基本识别功能测试

使用简单图像测试两个引擎的基本识别功能：

```dart
// 测试MLKit识别
final mlkitResults = await mlkitEngine.recognize(simpleImagePath);

// 测试PaddleOCR识别
final paddleResults = await paddleEngine.recognize(simpleImagePath);

// 验证结果不为空
expect(mlkitResults.isNotEmpty, true);
expect(paddleResults.isNotEmpty, true);
```

### 3. 复杂场景识别测试

使用复杂背景、反光、小字体等图像测试：

```dart
// 测试复杂图像识别
final complexResults = await adaptiveService.recognizeAdaptive(
  imagePath: complexImagePath,
  mode: RecognitionMode.precision,
);

// 验证识别结果质量
expect(complexResults.any((r) => r.confidence > 90), true);
```

### 4. 引擎选择策略测试

验证在不同场景下是否选择了正确的引擎：

```dart
// 测试简单场景选择MLKit
final simpleAnalysis = ImageComplexityAnalysis(
  complexityLevel: ComplexityLevel.low,
  backgroundColorAnalysis: BackgroundColorAnalysis(
    dominantColor: BackgroundColor.white,
    confidence: 0.9,
  ),
  reflectionScore: 0.1,
  // 其他属性...
);

final simpleEngine = adaptiveService.selectOptimalEngine(
  simpleAnalysis, 
  RecognitionMode.fast
);
expect(simpleEngine.name, 'MLKit');

// 测试复杂场景选择PaddleOCR
final complexAnalysis = ImageComplexityAnalysis(
  complexityLevel: ComplexityLevel.veryComplex,
  backgroundColorAnalysis: BackgroundColorAnalysis(
    dominantColor: BackgroundColor.dark,
    confidence: 0.9,
  ),
  reflectionScore: 0.7,
  // 其他属性...
);

final complexEngine = adaptiveService.selectOptimalEngine(
  complexAnalysis, 
  RecognitionMode.precision
);
expect(complexEngine.name, 'PaddleOCR');
```

### 5. 降级策略测试

验证当PaddleOCR失败时是否能正确降级到MLKit：

```dart
// 模拟PaddleOCR识别失败
// 系统应自动降级到MLKit并返回结果
final results = await adaptiveService.performRecognitionWithEngine(
  imagePath,
  failingPaddleEngine, // 模拟失败的PaddleOCR引擎
  RecognitionMode.standard,
  presetProductCode,
  presetBatchNumber,
  allBatches,
);

// 验证结果来自MLKit
expect(results.any((r) => r.metadata?['selectedEngine'] == 'MLKit'), true);
```

## 性能测试

### 1. 识别速度测试

测量不同引擎在不同场景下的识别时间：

```dart
final startTime = DateTime.now();
final results = await engine.recognize(imagePath);
final duration = DateTime.now().difference(startTime);

// 记录识别时间
print('${engine.name}识别耗时: ${duration.inMilliseconds}ms');
```

### 2. 准确率测试

使用已知文本的图像测试识别准确率：

```dart
final expectedText = "LOADGUARD-A123456789";
final results = await engine.recognize(testImagePath);
final recognizedText = results.first.ocrText;

// 检查识别结果是否匹配预期
expect(recognizedText, expectedText);
```

## 测试图像准备

准备以下类型的测试图像：

1. **简单场景**：
   - 白色背景，黑色文字
   - 清晰的大字体

2. **中等复杂度**：
   - 彩色背景
   - 中等大小字体

3. **复杂场景**：
   - 深色背景
   - 反光表面
   - 小字体
   - 多行文本

4. **特殊背景**：
   - 蓝色背景
   - 绿色背景
   - 红色背景
   - 紫色背景

## 自动化测试脚本

可以编写自动化测试脚本来批量测试不同场景：

```bash
#!/bin/bash
# dual_engine_test.sh

echo "开始双引擎OCR识别测试"

# 测试简单场景
flutter test test/simple_scenario_test.dart

# 测试复杂场景
flutter test test/complex_scenario_test.dart

# 测试降级策略
flutter test test/fallback_test.dart

# 测试性能
flutter test test/performance_test.dart

echo "双引擎OCR识别测试完成"
```

## 预期测试结果

通过全面的测试，我们期望达到以下指标：

1. **识别准确率**：
   - 简单场景：>95%
   - 复杂场景：>90%
   - 整体准确率：>92%

2. **识别速度**：
   - MLKit：平均500ms
   - PaddleOCR：平均1800ms
   - 智能选择后：平均800-1000ms

3. **降级成功率**：
   - PaddleOCR失败时100%降级到MLKit

4. **引擎选择准确率**：
   - 简单场景选择MLKit：>95%
   - 复杂场景选择PaddleOCR：>90%