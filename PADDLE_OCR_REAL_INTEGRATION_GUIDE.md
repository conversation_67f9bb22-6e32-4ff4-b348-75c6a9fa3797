# PaddleOCR真实集成指南

## 概述

本指南详细说明如何在LoadGuard项目中完成PaddleOCR的真实集成，替换掉原有的模拟实现。

## 集成步骤

### 1. 添加原生依赖

#### Android

1. 在 [android/app/build.gradle](file:///d:/weishi/android/app/build.gradle) 中添加PaddleOCR依赖：

```gradle
dependencies {
    // 其他依赖...
    implementation 'org.paddlepaddle:paddle-lite:2.11'
    implementation 'org.opencv:opencv-android:4.5.5'
}
```

2. 配置NDK和CMake：

```gradle
android {
    // 其他配置...
    
    defaultConfig {
        // 其他配置...
        
        ndk {
            abiFilters 'arm64-v8a', 'armeabi-v7a'
        }
    }
    
    externalNativeBuild {
        cmake {
            path "src/main/cpp/CMakeLists.txt"
            version "3.18.1"
        }
    }
}
```

#### iOS

1. 下载PaddleOCR iOS Framework并添加到项目中
2. 在Xcode中配置Framework链接

### 2. 添加模型文件

将以下模型文件添加到对应平台：

#### Android ([android/app/src/main/assets/models/](file:///d:/weishi/android/app/src/main/assets/models/))

- ch_PP-OCRv4_det_infer.nb (检测模型)
- ch_PP-OCRv4_rec_infer.nb (识别模型)
- ch_ppocr_mobile_v2.0_cls_infer.nb (分类模型)
- ppocr_keys_v1.txt (字典文件)

#### iOS (ios/Runner/models/)

- 同样的模型文件

### 3. 实现原生代码

#### Android

1. 创建 [PaddleOCREngine.kt](file:///d:/weishi/android/app/src/main/kotlin/com/example/loadguard/PaddleOCREngine.kt) 文件实现真实的PaddleOCR引擎：

```kotlin
class PaddleOCREngine(private val context: Context) {
    private var isInitialized = false
    private var modelPath: String? = null
    
    fun initialize(modelPath: String): Boolean {
        // 实现模型加载和引擎初始化
        // 返回初始化是否成功
    }
    
    fun recognize(imagePath: String): List<OcrResult> {
        // 实现OCR识别逻辑
        // 返回识别结果列表
    }
    
    fun release() {
        // 释放引擎资源
    }
}

data class OcrResult(
    val text: String,
    val confidence: Float,
    val boundingBox: List<List<Float>>,
    val direction: Int
)
```

2. 更新 [MainActivity.kt](file:///d:/weishi/android/app/src/main/kotlin/com/example/loadguard/MainActivity.kt) 文件，处理Flutter传来的MethodChannel调用：

```kotlin
MethodChannel(flutterEngine.dartExecutor.binaryMessenger, PADDLE_OCR_CHANNEL).setMethodCallHandler { call, result ->
    when (call.method) {
        "initialize" -> {
            // 初始化PaddleOCR引擎
        }

        "recognize" -> {
            // 执行OCR识别
        }

        "dispose" -> {
            // 释放资源
        }

        else -> {
            result.notImplemented()
        }
    }
}
```

3. 实现图像处理和识别逻辑
4. 通过JNI调用PaddleLite库

#### iOS

1. 创建PaddleOCRPlugin.swift文件实现iOS端的PaddleOCR集成
2. 实现与Flutter层的通信

### 4. 更新Flutter层

已经更新了 [lib/services/paddle_ocr_engine.dart](file:///d:/weishi/lib/services/paddle_ocr_engine.dart) 文件，使用真实的MethodChannel调用原生功能：

```dart
class PaddleOcrEngine implements OcrEngine {
  static const MethodChannel _channel = MethodChannel('paddle_ocr_engine');
  
  @override
  Future<void> initialize() async {
    // 调用原生平台进行真实初始化
    final bool result = await _channel.invokeMethod('initialize');
    // 处理结果
  }
  
  @override
  Future<List<RecognitionResult>> recognize(
    String imagePath, {
    Function(double progress, String status)? onProgress,
  }) async {
    // 调用原生平台进行真实识别
    final List<dynamic> rawResults = await _channel.invokeMethod('recognize', params);
    // 处理识别结果
  }
  
  @override
  Future<void> dispose() async {
    // 释放原生资源
    await _channel.invokeMethod('dispose');
  }
}
```

### 5. 构建和测试

1. 构建Android应用：
```bash
flutter build apk --release
```

2. 构建iOS应用：
```bash
flutter build ios --release
```

## 性能优化建议

1. **模型选择**：使用轻量级模型以平衡准确率和速度
2. **多线程处理**：利用多核CPU提高处理速度
3. **内存管理**：及时释放模型和图像资源
4. **缓存机制**：对重复图像使用缓存结果

## 故障排除

### 常见问题

1. **模型加载失败**：检查模型文件路径和完整性
2. **依赖冲突**：确保所有依赖版本兼容
3. **架构不匹配**：确保NDK配置与目标设备匹配

### 调试技巧

1. 查看原生日志输出
2. 使用性能分析工具检测瓶颈
3. 验证模型文件完整性

## 验证清单

- [ ] Android依赖已添加
- [ ] iOS Framework已集成
- [ ] 模型文件已部署
- [ ] 原生代码已实现
- [ ] Flutter层已更新
- [ ] 构建测试通过
- [ ] 功能测试通过

## 双引擎识别策略

LoadGuard项目实现了双引擎识别策略，根据以下因素智能选择最优识别引擎：

1. **图像复杂度**：
   - 低复杂度：优先选择MLKit
   - 高复杂度：优先选择PaddleOCR

2. **背景颜色**：
   - 蓝色、绿色、红色、紫色背景：加分给PaddleOCR
   - 深色背景：强烈加分给PaddleOCR
   - 其他背景：加分给MLKit

3. **反光程度**：
   - 高反光：加分给PaddleOCR
   - 低反光：加分给MLKit

4. **识别模式**：
   - 快速模式：强烈加分给MLKit
   - 标准模式：均衡考虑
   - 高精度模式：强烈加分给PaddleOCR

当PaddleOCR识别失败时，系统会自动降级到MLKit引擎，确保识别过程不会中断。