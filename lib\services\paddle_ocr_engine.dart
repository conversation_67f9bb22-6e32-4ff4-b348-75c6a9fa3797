import 'dart:async';
import 'package:flutter/services.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/services/ocr_engine.dart';
import 'package:loadguard/utils/app_logger.dart';

/// PaddleOCR引擎适配器
/// 
/// 集成百度PaddleOCR，提供高精度中英文识别和80+语言支持
/// 适用于复杂工业标签场景，特别是困难背景和小字体识别
class PaddleOcrEngine implements OcrEngine {
  static const MethodChannel _channel = MethodChannel('paddle_ocr_engine');
  
  bool _isInitialized = false;
  
  @override
  String get name => 'PaddleOCR';
  
  @override
  String get description => '百度PaddleOCR，高精度多语言识别，支持80+语言，适合复杂场景';
  
  @override
  bool get isInitialized => _isInitialized;
  
  @override
  Future<void> initialize() async {
    if (_isInitialized) {
      AppLogger.info('PaddleOCR引擎已初始化，跳过重复初始化');
      return;
    }
    
    try {
      AppLogger.info('🚀 初始化PaddleOCR引擎...');
      
      // 调用原生平台进行真实初始化
      final bool result = await _channel.invokeMethod('initialize');
      
      if (result) {
        _isInitialized = true;
        AppLogger.info('✅ PaddleOCR引擎初始化成功');
      } else {
        throw Exception('PaddleOCR引擎初始化失败');
      }
    } catch (e) {
      AppLogger.error('❌ PaddleOCR引擎初始化失败: $e');
      rethrow;
    }
  }
  
  @override
  Future<List<RecognitionResult>> recognize(
    String imagePath, {
    Function(double progress, String status)? onProgress,
  }) async {
    if (!_isInitialized) {
      throw const OcrEngineException(
        'PaddleOCR引擎未初始化',
        engineName: 'PaddleOCR',
      );
    }

    if (imagePath.isEmpty) {
      throw const OcrEngineException(
        '图像路径不能为空',
        engineName: 'PaddleOCR',
      );
    }
    
    onProgress?.call(0.1, '准备PaddleOCR识别...');
    
    try {
      // 调用原生平台进行真实识别
      onProgress?.call(0.3, '执行PaddleOCR识别...');
      
      final Map<String, dynamic> params = {
        'imagePath': imagePath,
      };
      
      final List<dynamic> rawResults = await _channel.invokeMethod('recognize', params);
      
      // 处理识别结果
      final results = <RecognitionResult>[];
      for (final rawResult in rawResults) {
        final Map<String, dynamic> result = rawResult as Map<String, dynamic>;
        results.add(RecognitionResult(
          ocrText: result['text'] as String,
          confidence: (result['confidence'] as double) * 100, // 转换为百分比
          status: RecognitionStatus.completed,
          boundingBox: {
            'left': result['boundingBox'][0][0],
            'top': result['boundingBox'][0][1],
            'right': result['boundingBox'][2][0],
            'bottom': result['boundingBox'][2][1],
          },
          metadata: {
            'engine': 'PaddleOCR',
            'processingTime': DateTime.now().millisecondsSinceEpoch,
            'highAccuracy': true,
            'direction': result['direction'],
          },
        ));
      }
      
      onProgress?.call(0.8, '处理识别结果...');
      onProgress?.call(1.0, 'PaddleOCR识别完成');
      
      AppLogger.info('🎯 PaddleOCR识别完成，找到${results.length}个文本区域');
      
      return results;
    } catch (e) {
      AppLogger.error('❌ PaddleOCR识别失败: $e');
      onProgress?.call(1.0, '识别失败: $e');
      
      if (e is PlatformException) {
        throw OcrEngineException(
          'PaddleOCR识别失败: ${e.message}',
          engineName: 'PaddleOCR',
          originalError: e,
        );
      } else if (e is TimeoutException) {
        throw const OcrEngineException(
          'PaddleOCR识别超时，请稍后重试',
          engineName: 'PaddleOCR',
        );
      } else {
        throw OcrEngineException(
          'PaddleOCR识别失败: $e',
          engineName: 'PaddleOCR',
          originalError: e,
        );
      }
    }
  }
  
  @override
  Future<void> dispose() async {
    if (_isInitialized) {
      try {
        await _channel.invokeMethod('dispose');
        _isInitialized = false;
        AppLogger.info('🔄 PaddleOCR引擎资源已释放');
      } catch (e) {
        AppLogger.error('PaddleOCR引擎资源释放失败: $e');
        _isInitialized = false; // 即使释放失败也要重置状态
      }
    }
  }
  
  @override
  EnginePerformanceMetrics getPerformanceMetrics() {
    return const EnginePerformanceMetrics(
      accuracy: 0.95,           // 95%准确率（基于PaddleOCR官方数据）
      avgProcessingTime: 1800,  // 平均1.8秒
      memoryUsage: 120.0,       // 120MB内存使用
      supportsQrCode: false,    // 专注文字识别，不支持二维码
      supportedLanguages: [
        'zh-CN', 'en', 'ja', 'ko', 'de', 'fr', 'es', 'pt', 'ru', 'ar',
        'hi', 'th', 'vi', 'it', 'nl', 'pl', 'uk', 'tr', 'he', 'fa'
      ],
    );
  }
}